﻿@model HalalaPlusProject.CModels.ServiceProvidersDetails
 @using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] = localizer["details"];
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h3>@localizer["details"]</h3>

<h4>
    @localizer["serviceprovider"]</h4>
    <hr />


    <!-- Tab links -->
<div class="tab">
    <button class="tablinks" onclick="openCity(event, 'DataTab')">@localizer["personaldata"]</button>
    <button class="tablinks" onclick="openCity(event, 'PointsTab')">@localizer["points"]  </button>
    <button class="tablinks" onclick="openCity(event, 'DiscountsTab')">@localizer["discounts"]</button>
    <button class="tablinks" onclick="openCity(event, 'AccountsTab')">@localizer["socialaccount"]</button>

</div>

<div id="AccountsTab" class="tabcontent" >
 <div class="row">
      <div class="col-md-8">
          <div class="col-md-12">
                 <div class="table-responsive p-0">
               
                <table class="table table-striped text-center">
                  <thead>
                    <tr>


                                <th scope="col">@localizer["platformname"] </th>
                                <th scope="col">@localizer["platformlink"]</th>
                    @*  <th scope="col"> خيارات</th>*@
                    </tr>
                  </thead>
                  <tbody>
                  @foreach (var item in Model.Accounts) {
                    <tr id="@item.Id">
            
           
                    <td>
                        @Html.DisplayFor(modelItem => item.Name)
                    </td>
                    <td>
                          @Html.DisplayFor(modelItem => item.Link) 
                    </td>
                    <td>
                           @* <a onclick="EditSite(@item.Id)" class="btn btn-outline-info tablebtn" >تعديل</a> |          
                            <a href="#" class="btn btn-outline-danger tablebtn"  onclick="DeleteAccount('@item.Id')">حذف</a>*@
                    </td>
                     </tr>
}                  
                  </tbody>              
                </table>
              </div>
              
            </div>
           </div>
           </div>
      </div>


<div id="PointsTab" class="tabcontent" >
 <div class="row">

    
    <div class="col-md-3 col-sm-6  ">
       
             <div class="row">
                   <div class="col-md-12">
                    <div class="form-group">
                        <label asp-for="sales.SalesNo" class="control-label">@localizer["salesno"]</label>
                        <input asp-for="sales.SalesNo" name="SalesNo" class="form-control" />
                        <span asp-validation-for="sales.SalesNo"  class="text-danger"></span>
                    </div>
                      <div class="form-group">
                        <label asp-for="sales.DeservePoints" class="control-label">@localizer["deservpoints"]</label>
                        <input asp-for="sales.DeservePoints" name="DeservePoints" class="form-control" />
                        <span asp-validation-for="sales.DeservePoints"  class="text-danger"></span>
                    </div>



                 </div>
                  @*<div class="col-md-3">
                    <div class="form-group">
                        <label asp-for="Points.PointNo" class="control-label"></label>
                        <input asp-for="Points.PointNo" name="PointNo" class="form-control" />
                        <span asp-validation-for="Points.PointNo"  class="text-danger"></span>
                    </div>
                      <div class="form-group">
                        <label asp-for="Points.Prize" class="control-label"></label>
                        <input asp-for="Points.Prize" name="Prize" class="form-control" />
                        <span asp-validation-for="Points.Prize"  class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="Points.PointsConditions" class="control-label"></label>
                        <textarea asp-for="Points.PointsConditions" name="PointsConditions" class="form-control" ></textarea>
                        <span asp-validation-for="Points.PointsConditions"  class="text-danger"></span>
                    </div>


                 </div>*@
                 </div>
                
            </div>
                    <div class="col-md-6 col-sm-12 col-xs-12">
               <div class="table-responsive p-0">
               
                <table class="table table-striped text-center">
                  <thead>
                    <tr>


                            <th scope="col">@localizer["pointsno"]</th>
                            <th scope="col">@localizer["prize"]</th>
                            <th scope="col"> @localizer["conditions"]</th>
                      
                    </tr>
                  </thead>
                  <tbody>
                  @foreach (var item in Model.Points) {
        <tr>
            
           
            <td>
                @Html.DisplayFor(modelItem => item.PointNo)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Prize)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.PointsConditions)
            </td>
          
             
        </tr>
}                  
                  </tbody>              
                </table>
              </div>
            </div>
            </div>
</div>
<div id="DiscountsTab" class="tabcontent" >
 <div class="row">
   
            <div class="col-md-12">
               @Html.Partial("_DiscountsDetails",Model.DiscountsList)
               
            </div>
            </div>
</div>
<div id="DataTab" class="tabcontent" style="display:block">
  
   <div class="col-md-12">
         <div class="row">

     
     <div class="col-md-3">
                <div class="form-group">
                    <label class="form-label">@localizer["contractno"]</label>
                    <label class="form-control">   @Html.DisplayFor(model => model.ContractNo)</label>
                </div>
          <div class="form-group">
                    <label class="form-label">@localizer["serviceprovidername"]</label>
                <label  class="form-control">   @Html.DisplayFor(model => model.Name)</label>               
            </div>
                <div class="form-group">
                    <label class="form-label">@localizer["serviceprovidername"] -En</label>
                    <label class="form-control">   @Html.DisplayFor(model => model.EnName)</label>
                </div>
          <div class="form-group">
                    <label class="form-label">@localizer["activity"]</label>
                <label  class="form-control">   @Html.DisplayFor(model => model.ActivityName)</label>               
            </div>
                <div class="form-group">
                    <label class="form-label">@localizer["serviceproviderrepresent"]</label>
                    <label class="form-control">   @Html.DisplayFor(model => model.ServiceProviderRepresent)</label>
                </div>
            
          <div class="form-group">
                    <label class="form-label">@localizer["thecity"]</label>
                <label  class="form-control">   @Html.DisplayFor(model => model.City)</label>               
            </div>
               <div class="form-group">
                    <label class="form-label">@localizer["storelink"]</label>
                <label  class="form-control">   @Html.DisplayFor(model => model.StoreLink)</label>               
            </div>
              
           

        </div>
           


            <div class="col-md-3">
                <div class="form-group">
                    <label class="form-label">@localizer["phoneno"]</label>
                    <label class="form-control">   @Html.DisplayFor(model => model.PhoneNumber)</label>
                </div>

                <div class="form-group">
                    <label class="form-label">@localizer["contractstartdate"]</label>
                    <label class="form-control">   @Html.DisplayFor(model => model.ContractDate)</label>
                </div>
                <div class="form-group">
                    <label class="form-label">@localizer["contractenddate"]</label>
                    <label class="form-control">   @Html.DisplayFor(model => model.ContractEndDate)</label>
                </div>  
                <div class="form-group">
                    <label class="form-label">@localizer["overview"]</label>
                    <label class="form-control">   @Html.DisplayFor(model => model.overview)</label>
                </div>
                <div class="form-group">
                    <label class="form-label">@localizer["enstoreoverview"]</label>
                    <label class="form-control">   @Html.DisplayFor(model => model.enoverview)</label>
                </div> 
                <div class="form-group">
                    <label class="form-label">@localizer["bnifitfrompoints"]</label>
                    <label class="form-control">   @Html.DisplayFor(model => model.bnifitfrompoints)</label>
                </div>
                <div class="form-group">
                    <label class="form-label">@localizer["enbnifitfrompoints"]</label>
                    <label class="form-control">   @Html.DisplayFor(model => model.enbnifitfrompoints)</label>
                </div>

            </div>
         <div class="col-md-3">
                <div class="form-group">
                    <label class="form-label">@localizer["logo"]</label><br />
                    <img style="width:168px" src="@Model.Logo" />
                </div>
                <div class="form-group">
                    <label class="form-label">@localizer["busnissno"]</label>
                <label  class="form-control">   @Html.DisplayFor(model => model.BusnissNo)</label>               
            </div>
            <div class="form-group">
                    <label class="form-label">@localizer["enterprisephoneno"]</label>
                <label  class="form-control">   @Html.DisplayFor(model => model.EnterprisePhoneNo)</label>               
            </div>
              
             
        
           
            
           
          

           
              

            </div>
            <div class="col-md-3">
                <div class="form-group">
                    <div style="height: 400px; max-width: 400px;" id="map">
                    </div>
                </div>
            </div>

            </div>
       
              <div class="row mt-2">
                 

                   <div class="col-md-6">
                 <div class="table-responsive p-0">
               
                <table class="table table-striped text-center">
                  <thead>
                    <tr>


                                <th scope="col">@localizer["platformname"]</th>
                                <th scope="col">@localizer["platformlink"]</th>
                    </tr>
                  </thead>
                  <tbody>
                  @foreach (var item in Model.Accounts) {
        <tr>
            
           
            <td>
                @Html.DisplayFor(modelItem => item.Name)
            </td>
            <td>
                  @Html.DisplayFor(modelItem => item.Link) 
            </td>
        </tr>
}                  
                  </tbody>              
                </table>
              </div>
              
            </div>


             <div class="col-md-6">
                 <div class="table-responsive p-0">
               
                <table class="table table-striped text-center">
                  <thead>
                    <tr>


                                <th scope="col">@localizer["images"]</th>
                                <th scope="col">@localizer["display"]</th>
                    </tr>
                  </thead>
                  <tbody>
                  @foreach (var item in Model.images) {
        <tr>
            
           
            <td>
                @Html.DisplayFor(modelItem => item.Name)
            </td>
            <td>

                                        <a class="view" target="_blank" href="@item.Link">@localizer["displayimages"]</a>
            </td>
        </tr>
}                  
                  </tbody>              
                </table>
              </div>
              
            </div>
            <div class="col-md-6">
                 <div class="table-responsive p-0">
               
                <table class="table table-striped text-center">
                  <thead>
                    <tr>


                                <th scope="col">@localizer["filename"]</th>
                                <th scope="col">@localizer["display"]</th>
                    </tr>
                  </thead>
                  <tbody>
                  @foreach (var item in Model.Files) {
        <tr>
            
           
            <td>
                @Html.DisplayFor(modelItem => item.Name)
            </td>
            <td>

                                        <a class="view" href="@item.Link">@localizer["displayfile"]</a>
            </td>
        </tr>
}                  
                  </tbody>              
                </table>
              </div>
              
            </div>
            
            </div>
     
          

          </div>
          </div>


1
<div>
   
    <a asp-action="Edit" class="btn btn-primary px-5" asp-route-id="@Model?.Id">@localizer["edit"]</a> |
    <a asp-action="Delete" class="btn btn-primary px-5" asp-route-id="@Model?.Id">@localizer["delete"]</a> |
    <a class="btn btn-primary px-5" asp-action="Index">@localizer["backtolist"]</a>
</div>
<script>
    (g => { var h, a, k, p = "The Google Maps JavaScript API", c = "google", l = "importLibrary", q = "__ib__", m = document, b = window; b = b[c] || (b[c] = {}); var d = b.maps || (b.maps = {}), r = new Set, e = new URLSearchParams, u = () => h || (h = new Promise(async (f, n) => { await (a = m.createElement("script")); e.set("libraries", [...r] + ""); for (k in g) e.set(k.replace(/[A-Z]/g, t => "_" + t[0].toLowerCase()), g[k]); e.set("callback", c + ".maps." + q); a.src = `https://maps.${c}apis.com/maps/api/js?` + e; d[q] = f; a.onerror = () => h = n(Error(p + " could not load.")); a.nonce = m.querySelector("script[nonce]")?.nonce || ""; m.head.append(a) })); d[l] ? console.warn(p + " only loads once. Ignoring:", g) : d[l] = (f, ...n) => r.add(f) && u().then(() => d[l](f, ...n)) })
        ({ key: "AIzaSyBRDq842x31n-rAmjPQ7hZotsZaGvbjI_U", v: "weekly" });</script>


<script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyBRDq842x31n-rAmjPQ7hZotsZaGvbjI_U&language=ar&callback=initMap&libraries=&v=weekly"
        async></script>
<script>
    async function initMap() {

        // Request needed libraries.
        const { Map } = await google.maps.importLibrary("maps");
        const myLatlng1 = { lat: 20.99265387585728, lng: 40.54420880475375 };
        const myLatlng = { lat: @Model.Lat, lng: @Model.Lng };

        const map = new google.maps.Map(document.getElementById("map"), {
            zoom: 6,
            center: myLatlng,
        });
        // var marker1 = new google.maps.Marker({
        //     position: myLatlng,
        //     map: map,
        //     title:'hh'
        // });
        // Create the initial InfoWindow.
        let infoWindow = new google.maps.InfoWindow({
            // content: "Click the map to get Lat/Lng!",
            position: myLatlng,
            name: 'KSA'
        });
        var marker = new google.maps.Marker({
            position: myLatlng,
            map: map,
            title: 'Location'
        });


        // Configure the click listener.
        map.addListener("click", (mapsMouseEvent) => {
            infoWindow.open(map);
            document.getElementById('Lat').value = mapsMouseEvent.latLng.lat();
            document.getElementById('lng').value = mapsMouseEvent.latLng.lng();
            // console.log("clicked:",myLatlng);
            // Close the current InfoWindow.
            var result = {
                lat: mapsMouseEvent.latLng.lat(),
                lng: mapsMouseEvent.latLng.lng()
            };
            // marker1 = new google.maps.Marker({
            //     position: result,
            //     map: map,
            //     title: 'ss'
            // });
            infoWindow.close();
            // Create a new InfoWindow.
            infoWindow = new google.maps.InfoWindow({
                position: mapsMouseEvent.latLng,
            });
            infoWindow.setContent(
                JSON.stringify(mapsMouseEvent.latLng.toJSON(), null, 2),
            );
            infoWindow.open(map);
        });
    }

    initMap();
</script>
