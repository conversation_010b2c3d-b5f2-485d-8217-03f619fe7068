﻿@model IEnumerable<HalalaPlusProject.CModels.UsersModel>
@using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] = localizer["markters"];
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h1>@localizer["markters"]</h1>

<p>
    <a asp-action="Create" class="btn btn-primary"> @localizer["createmarkter"]</a>
</p>
<table id="tbl1" class="table">
    <thead>
             <tr>
            <th scope="col">@localizer["name"]</th>
            <th scope="col">@localizer["nationality"]</th>
            <th scope="col">@localizer["phoneno"]</th>
            <th scope="col">@localizer["mail"]</th>
            <th scope="col">@localizer["username"]</th>
            <th scope="col">@localizer["more"]</th>
                    </tr>
            
    </thead>
    <tbody>
@foreach (var item in Model) {
        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.Name)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Nationality)
            </td>
         
            <td>
                @Html.DisplayFor(modelItem => item.PhoneNo)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Email)
            </td>
           <td>
                @Html.DisplayFor(modelItem => item.UserName)
            </td>
           
           
          
            <td>
                    <a asp-action="Details" asp-route-id="@item.Id">@localizer["more"]</a>
            </td>
        </tr>
}
    </tbody>
</table>
@section Scripts{
    <script>
  let table = new DataTable('#tbl1');
  

    </script>
}
