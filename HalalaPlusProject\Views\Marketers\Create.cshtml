﻿@model HalalaPlusProject.CModels.MarketerModel
@using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer
@{
    ViewData["Title"] = localizer["create"];
    Layout = "~/Views/Shared/_Layout.cshtml";
}


<div>
    <a asp-action="Index"><img src="../assets/img/svgs/solid/arrow-right.svg" style="width: 30px;" alt=""></a>
    <h3>@localizer["createmarkter"]</h3>

</div>
<div class="row">
    <div class="offset-md-2 col-md-8 col-sm-12">
        <form asp-action="Create" class="submitfm">
            <div class="row">
                <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            </div>

            <div class="row">
                <div class="col-md-5">
                    <div class="form-group">
                        <label asp-for="Name" class="control-label">@localizer["marktername"]</label>
                        <input asp-for="Name" class="form-control" />
                        <span asp-validation-for="Name" required class="text-danger"></span>
                    </div>

                    <div class="form-group">
                        <label asp-for="Nationality" class="control-label">@localizer["nationlity"]</label>
                        <select asp-for="Nationality" class="form-select" asp-items="ViewBag.National"></select>
                        <span asp-validation-for="Nationality" required class="text-danger"></span>
                    </div>

                    <div class="form-group">
                        <label asp-for="IdentityNo" class="control-label">@localizer["identityno"] </label>
                        <input asp-for="IdentityNo" class="form-control" />
                        <span asp-validation-for="IdentityNo" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="BirthDate" class="control-label">@localizer["birthdate"]</label>
                        <input asp-for="BirthDate" type="date" class="form-control" />
                        <span asp-validation-for="BirthDate" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="Precentage" class="control-label">@localizer["dealingway"]</label>
                        <input asp-for="Precentage" placeholder="20%" class="form-control" />
                        <span asp-validation-for="Precentage" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <input asp-for="Amount" placeholder="2000" class="form-control" />
                        <span asp-validation-for="Amount" class="text-danger"></span>
                    </div>

                </div>

                <div class="col-md-5">
                    <div class="form-group">
                        <label asp-for="DiscountCode" class="control-label">@localizer["coupon"] </label>
                        <input asp-for="DiscountCode" class="form-control" />
                        <span asp-validation-for="DiscountCode" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="PhoneNo" class="control-label">@localizer["phoneno"]</label>
                        <input asp-for="PhoneNo" class="form-control" />
                        <span asp-validation-for="PhoneNo" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="Email" class="control-label">@localizer["mail"]</label>
                        <input asp-for="Email" class="form-control" />
                        <span asp-validation-for="Email" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="UserName" class="control-label">@localizer["username"]</label>
                        <input asp-for="UserName" class="form-control" />
                        <span asp-validation-for="UserName" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="Password" class="control-label">@localizer["password"]</label>
                        <input asp-for="Password" class="form-control" />
                        <span asp-validation-for="Password" class="text-danger"></span>
                    </div>


                </div>
            </div>









            <div class="form-group mt-2">
                <button type="submit" value="Create" class="btn btn-primary">@localizer["create"]</button>
            </div>
        </form>
    </div>
</div>



@section Scripts {
    @{
        await Html.RenderPartialAsync("_ValidationScriptsPartial");
    }
}